<template>
    <div class="home-rebrand">
        <hero-banner-b v-if="!isMobile" />
        <HomeMwebSearch v-else @toggleSidebar="$emit('toggleSidebar')"
          preTitle="Looking for Best Car Rentals?"
          :title="`Book Self-Drive Cars in ${cityDisplay}`"
        />
        <MwebPopup
            v-if="false"
            @onClose="handlePopupClose"
        />
    <!-- microsite banners -->
    <smartbuy-banner v-if="smartbuy" />
    <AirindiaBanner v-if="airindia" />
    <AirindiaexpressBanner v-if="airindiaexpress" />
    <YonosbiBanner v-if="yonosbi" />
    <BajajFinservBanner v-if="bajajfinserv" />
    <WegoBanner v-if="wegobanner" />

    <!-- top cars -->
    <home-carousel
      v-if="carsData.length >= 3"
      :cards="carsData"
      :visibleCards="4"
      :heading="'Top cars in ' + cityDisplay"
      buttonText="Browse all cars"
      id="car-recommendation"
      @click="handleGetCarClick('top cars')"
    >
      <template v-slot:default="{ card, index }">
        <!-- <new-car-card
                  :card="card"
                  @cardClick="handleCarClick(card)"
                  @login="(callback) => {
                    $emit('login', callback)
                  }"  
                /> -->
        <car-item-search
          :car="card"
          :key="card.car_data.car_id"
          :itemId="card.car_data.car_id"
          :lastCar="lastCar(index)"
          :showFareSummary="false"
          @submit="handleCarSelection"
          @handleSegment="handleSegment"
          @handleFavClick="handleFavClick"
          :showFav="true"
        />
      </template>
    </home-carousel>

    <!-- top catgories -->
    <top-categories @click="handleGetCarClick" />

        <!-- car categories -->
        <div class="car-categories" v-if="isCityData">
            <div class="car-categories__header">
                <div class="needle"></div>
                <h2 class="car-categories__header-text">{{ city_data[city].car_categories.title }}</h2>
                <div class="needle-2"></div>
            </div>
            <div class="car-categories__sub-text">
                {{ city_data[city].car_categories.sub_text }}
            </div>
            <div class="car-categories__cards">
                <div class="car-categories__card" v-for="card in city_data[city].car_categories.cards" :key="card.title">
                    <div class="car-categories__card-heading">{{ card.title }}</div>

          <picture class="car-categories__card--img" v-if="isMobile">
            <source
              :srcSet="car_categories_imgs[card.type.toUpperCase()].webp"
              type="image/webp"
            />
            <img
              loading="lazy"
              :src="car_categories_imgs[card.type.toUpperCase()].img"
              :alt="card.type"
            />
          </picture>
          <div class="car-categories__card-text">{{ card.text }}</div>
          <div
            class="car-categories__card-cta"
            @click="handleGetCarClick('car_category', 'car_type', card.type)"
          >
            {{ card.cta }}
          </div>
        </div>
      </div>
    </div>

    <!-- offers-->
    <home-offers @click="handleGetCarClick" />

         <!-- short term -->
         <div class="short-term" v-if="isCityData">
            <div class="short-term__city">
                <div class="short-term__city--info-mobile-heading">
                        <h2 class="short-term__city--info-mobile-heading-text">{{ city_data[city].short_term.title }}</h2>
                        <div class="needle-2"></div>
                </div>
                <picture class="short-term__city--image">
                        <source  :srcSet="city_data[city].short_term.image" type="image/webp">
                        <img  loading="lazy" :src="city_data[city].short_term.fallback" :alt="city" />
                </picture>
                <div class="short-term__city--info">
                    <div class="short-term__city--info-heading">
                        <h2 class="short-term__city--info-heading-text">{{ city_data[city].short_term.title }}</h2>
                        <div class="needle-2"></div>
                    </div>
                    <div class="short-term__city--info-text">{{ city_data[city].short_term.info_text }}</div>
                    <div class="short-term__city--info-text">{{ city_data[city].short_term.info_text_2 }}</div>
                    <div class="short-term__city--info-cta" @click="shortTermClick()">{{ city_data[city].short_term.cta }}</div>
                </div>

            </div>
            <div class="short-term__cards">
                <div class="short-term__cards-sub-heading"  v-if="city=='bangalore'">
                    Looking for Short-term Car Rentals?
                </div>
                <h2 class="short-term__cards-heading">
                    <div class="short-term__cards-heading-text">{{ city_data[city].short_term.cards_data.title }}</div>
                    <div class="short-term__cards-heading-price">{{ city_data[city].short_term.cards_data.price }}</div>
                </h2>
                <div class="short-term__cards-sub-heading"  v-if="city !=='bangalore'">
                    {{ city_data[city].short_term.cards_data.sub_text }}
                </div>
                <div class="short-term__cards-section">
                    <div class="short-term__card" v-for="card in shortTermCards" @click="shortTermClick" :key="card.title">
                        <picture class="short-term__card-image" v-if="!isMobile">
                            <source :srcSet="card.image" type="image/webp">
                            <img  :src="card.fallback" :alt="card.title" />
                        </picture>
                        <div class="short-term__card-title">{{ card.title }} <i class="z-arrow_right_top" v-if="!isMobile"></i></div>
                        <div class="short-term__card-text">{{ card.text }}</div>
                    </div>
                </div>
                <div class="short-term__cards-view-more" v-if="viewMore" @click="viewMore=false">view more <i class="z-chevron_down"></i></div>
                <div class="short-term__cards-view-less" v-else @click="viewMore=true">view less <i class="z-chevron_up"></i></div>


            </div>
        </div>

    <!-- blogs -->

    <home-carousel
      v-if="blogs.length"
      :cards="blogs"
      :visibleCards="blogs.length"
      :heading="blogHeading"
      id="blogs"
      buttonText="DISCOVER MORE"
      @click="handleBlogCtaClick"
    >
      <template v-slot:default="{ card }">
        <div
          class="blog-card"
          :key="card.id"
          @click="handleBlogCardClick(card)"
        >
          <BlogSvg
            :imageUrl="card.image"
            :id="`${card.id}_image`"
            :key="`${card.id}_image`"
            :isMobile="isMobile"
          />
          <div class="blog-card-title" :key="`${card.id}_title`">
            {{ card.title }}
          </div>
          <div
            class="blog-card-subtitle"
            v-html="card.subTitle"
            :key="`${card.id}_subtitle`"
          ></div>
        </div>
      </template>
    </home-carousel>

        <div class="marketing" v-if="marketingBanners.length">
            <home-carousel :cards="marketingBanners" id="marketing" :heading="isMobile ? 'Explore Our Tribe' : ''">
                <template v-slot:default="{ card }">
                    <div class="marketing-card" @click="handleBannerClick(card.page)">
                        <picture>
                            <source :srcSet="card.image">
                            <img :src="card.fallbackImage" :alt="card.name" />
                        </picture>
                    </div>
                </template>
            </home-carousel>
        </div>

    <!-- stories -->
    <stories />

    <!-- top cities -->
    <home-carousel
      :cards="cities_cards"
      :visibleCards="4"
      heading="Zoom around all over India"
      id="cities"
    >
      <template v-slot:default="{ card }">
        <div class="city-card" @click="handleTopCityClick(card.link)">
          <picture>
            <source :srcSet="card.image" />
            <img loading="lazy" :src="card.fallbackImage" :alt="card.city" />
          </picture>
          <div class="city-card__gradient-overlay"></div>
          <div class="city-card-name">{{ card.city }}</div>
        </div>
      </template>
    </home-carousel>

    <!-- downnload banner -->
    <div
      v-if="!micrositeFlow"
      class="home-rebrand-app-download-banner"
      id="app-download-banner"
    >
      <picture>
        <source srcSet="/img/download-app-banner.webp" />
        <img
          loading="lazy"
          src="/img/download-app-banner.png"
          alt="Download app banner"
        />
      </picture>
    </div>

    <!-- host banner -->
    <div
      class="home-rebrand-host-banner"
      @click="openHostPage()"
      id="host-banner"
    >
      <picture>
        <source srcSet="/img/host-banner.webp" />
        <img
          loading="lazy"
          src="/img/host-banner.png"
          alt="Become a host banner"
        />
      </picture>
    </div>

        <!-- faq  -->
        <div class="faq-header">
            <div class="needle"></div>
            <h2 class="faq-header-text">Planning Your Car Rental? Check Our FAQs</h2>
            <div class="needle-2"></div>
        </div>
        <accordion-home :list="faqs" section="faq" :routeParams="{city: cityDisplay}" />

    <FeaturedIn />
    
    <!-- footer -->
    <footer-new :routeParams="{city: cityDisplay}"/>
    <FloatingYoutubePlayer
      v-if="showVideo && (!isMobile || (isMobile && !shouldShowMwebPopup))"
      videoId="6ft07Xy2BlE"
    />
  </div>
</template>
<script>
import "./home-rebrand.scss";
import HomeRebrand from "./home-rebrand";
export default HomeRebrand;
</script>
